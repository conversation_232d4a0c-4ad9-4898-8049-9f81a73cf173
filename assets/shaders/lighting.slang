module lighting;

import constants;

// Basic ambient + diffuse lighting calculation (from your existing code)
static float4 calculate_basic_lighting(float3 normal, float4 baseColor) {
    float3 N = normalize(normal);
    float3 L = normalize(float3(1, 1, 1));

    float ambientIntensity = kAmbientIntensity;
    float diffuseIntensity = saturate(dot(N, L));

    return float4(baseColor.rgb * saturate(ambientIntensity + diffuseIntensity), baseColor.a);
}
