module lighting;

import constants;
import utils;

// Basic directional light structure
struct DirectionalLight {
    float3 direction;
    float3 color;
    float intensity;
};

// Point light structure
struct PointLight {
    float3 position;
    float3 color;
    float intensity;
    float range;
};

// Material properties
struct Material {
    float3 albedo;
    float metallic;
    float roughness;
    float3 emissive;
};

// Simple Lambert diffuse lighting calculation
static float calculate_lambert_diffuse(float3 normal, float3 lightDirection) {
    return saturate(dot(normalize(normal), normalize(-lightDirection)));
}

// Basic ambient + diffuse lighting with a fixed directional light
static float3 calculate_basic_lighting(float3 normal, float3 baseColor) {
    float3 lightDirection = normalize(float3(1, 1, 1));
    float diffuseIntensity = calculate_lambert_diffuse(normal, lightDirection);
    return baseColor * saturate(kAmbientIntensity + diffuseIntensity);
}

// Enhanced lighting with configurable directional light
static float3 calculate_directional_lighting(float3 normal, float3 baseColor, DirectionalLight light) {
    float diffuseIntensity = calculate_lambert_diffuse(normal, light.direction) * light.intensity;
    float3 diffuse = baseColor * light.color * diffuseIntensity;
    float3 ambient = baseColor * kAmbientIntensity;
    return ambient + diffuse;
}

// Point light attenuation calculation
static float calculate_point_light_attenuation(float distance, float range) {
    float attenuation = 1.0 - saturate(distance / range);
    return attenuation * attenuation;
}

// Point light contribution calculation
static float3 calculate_point_lighting(float3 worldPos, float3 normal, float3 baseColor, PointLight light) {
    float3 lightVector = light.position - worldPos;
    float distance = length(lightVector);
    float3 lightDirection = lightVector / distance;
    
    float attenuation = calculate_point_light_attenuation(distance, light.range);
    float diffuseIntensity = calculate_lambert_diffuse(normal, -lightDirection) * light.intensity * attenuation;
    
    return baseColor * light.color * diffuseIntensity;
}

// Simple Blinn-Phong specular calculation
static float calculate_blinn_phong_specular(float3 normal, float3 lightDirection, float3 viewDirection, float shininess) {
    float3 halfVector = normalize(-lightDirection + viewDirection);
    return pow(saturate(dot(normalize(normal), halfVector)), shininess);
}
