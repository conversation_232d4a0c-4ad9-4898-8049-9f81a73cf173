module structures;

import constants;

// Basic vertex structure for input geometry
struct BasicVertex {
    float3 position : POSITION;
    float3 normal : NORMAL;
    float2 texCoords : UV;
};

// Per-object data, like total meshlet count
struct MeshData {
    uint meshletCount;
};

// Output from Mesh Shader, Input to Fragment Shader (per-vertex)
struct MeshletVertex {
    float4 position   : SV_Position;
    float3 normal     : NORMAL;
    float2 texCoords  : TEXCOORD0;
};

// Output from Mesh Shader, Input to Fragment Shader (per-primitive)
struct MeshletPrimitive {
    nointerpolation float4 color : COLOR0;
};

// Meshlet descriptor containing geometry and culling information
struct MeshletDescriptor {
    uint vertexOffset;
    uint vertexCount;
    uint triangleOffset;
    uint triangleCount;
    float3 boundsCenter;
    float boundsRadius;
    float3 coneApex;
    float3 coneAxis;
    float coneCutoff, pad;
};

// Payload shared between task and mesh shaders
struct ObjectPayload {
    uint meshletIndices[kMeshletsPerObject];
};

// Fragment Shader Input structure
struct FragmentIn {
    float4 position     : SV_Position;
    float3 normal       : NORMAL;
    float2 texCoords    : TEXCOORD0;
    nointerpolation float4 color : COLOR0;
};

// Constant buffer for instance-specific data
cbuffer InstanceData : register(b1) {
    float4x4 modelViewProjectionMatrix;
    float4x4 inverseModelViewMatrix;
    float4x4 normalMatrix;
};
