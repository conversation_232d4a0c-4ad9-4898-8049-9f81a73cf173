module task_shader;

#include "common_constants.slang"
#include "common_structures.slang"
#include "common_utils.slang"

// GPU Buffers
StructuredBuffer<MeshletDescriptor> g_meshlets : register(t0);
StructuredBuffer<MeshData> g_meshData : register(t2);

// Payload shared between task and mesh shaders
groupshared ObjectPayload g_payload;

// Task Shader (Object Shader in Metal terminology)
[shader("task")]
[numthreads(32, 1, 1)]
void task_main(
    uint3 dispatchThreadID : SV_DispatchThreadID,
    uint groupThreadID : SV_GroupThreadID)
{
    uint meshletIndex = dispatchThreadID.x;

    if (meshletIndex >= g_meshData[0].meshletCount) {
        return;
    }

    MeshletDescriptor meshlet = g_meshlets[meshletIndex];

    // Perform culling tests
    float4 frustumPlanes[6];
    extract_frustum_planes(modelViewProjectionMatrix, frustumPlanes);
    bool frustumCulled = !sphere_intersects_frustum(frustumPlanes, meshlet.boundsCenter, meshlet.boundsRadius);

    float3 cameraPosition = inverseModelViewMatrix[3].xyz;
    bool normalConeCulled = cone_is_backfacing(meshlet.coneApex, meshlet.coneAxis, meshlet.coneCutoff, cameraPosition);

    int passed = (!frustumCulled && !normalConeCulled) ? 1 : 0;
    
    // Compute payload index using wave prefix sum
    int payloadIndex = WavePrefixSum(passed);

    if (passed != 0) {
        g_payload.meshletIndices[payloadIndex] = meshletIndex;
    }
    
    // Count visible meshlets in this wave
    uint visibleMeshletCount = WaveActiveSum(passed);
    
    // Dispatch mesh shader grid
    if (groupThreadID == 0) {
        DispatchMesh(visibleMeshletCount, 1u, 1u, g_payload);
    }
}
