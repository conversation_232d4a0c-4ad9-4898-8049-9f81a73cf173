// Common utility functions used across shaders
#ifndef COMMON_UTILS_SLANG
#define COMMON_UTILS_SLANG

// Extracts the six frustum planes determined by the provided matrix.
static void extract_frustum_planes(float4x4 matrix, out float4 planes[6]) {
    float4x4 mt = transpose(matrix);
    planes[0] = mt[3] + mt[0]; // left
    planes[1] = mt[3] - mt[0]; // right
    planes[2] = mt[3] - mt[1]; // top
    planes[3] = mt[3] + mt[1]; // bottom
    planes[4] = mt[2];         // near
    planes[5] = mt[3] - mt[2]; // far
    for (int i = 0; i < 6; ++i) {
        planes[i] /= length(planes[i].xyz);
    }
}

// Tests if a sphere intersects with the frustum defined by the given planes
static bool sphere_intersects_frustum(float4 planes[6], float3 center, float radius) {
    for(int i = 0; i < 6; ++i) {
        if (dot(center, planes[i].xyz) + planes[i].w < -radius) {
            return false;
        }
    }
    return true;
}

// Tests if a cone is backfacing relative to the camera position
static bool cone_is_backfacing(float3 coneApex, float3 coneAxis, float coneCutoff, float3 cameraPosition) {
    return (dot(normalize(coneApex - cameraPosition), coneAxis) >= coneCutoff);
}

// Converts a hue value to RGB color
static float3 hue2rgb(float hue) {
    hue = frac(hue);
    float r = abs(hue * 6 - 3) - 1;
    float g = 2 - abs(hue * 6 - 2);
    float b = 2 - abs(hue * 6 - 4);
    float3 rgb = float3(r, g, b);
    rgb = saturate(rgb);
    return rgb;
}

#endif // COMMON_UTILS_SLANG
