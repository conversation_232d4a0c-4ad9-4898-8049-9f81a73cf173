module culling;

import structures;
import utils;

// Performs frustum culling on a meshlet
static bool is_meshlet_frustum_culled(MeshletDescriptor meshlet, float4x4 mvpMatrix) {
    float4 frustumPlanes[6];
    extract_frustum_planes(mvpMatrix, frustumPlanes);
    return !sphere_intersects_frustum(frustumPlanes, meshlet.boundsCenter, meshlet.boundsRadius);
}

// Performs backface culling on a meshlet using normal cone
static bool is_meshlet_backface_culled(MeshletDescriptor meshlet, float3 cameraPosition) {
    return cone_is_backfacing(meshlet.coneApex, meshlet.coneAxis, meshlet.coneCutoff, cameraPosition);
}

// Comprehensive culling test for a meshlet
static bool is_meshlet_culled(MeshletDescriptor meshlet, float4x4 mvpMatrix, float3 cameraPosition) {
    bool frustumCulled = is_meshlet_frustum_culled(meshlet, mvpMatrix);
    bool backfaceCulled = is_meshlet_backface_culled(meshlet, cameraPosition);
    return frustumCulled || backfaceCulled;
}

// Structure to hold culling results
struct CullingResult {
    bool isCulled;
    bool frustumCulled;
    bool backfaceCulled;
};

// Detailed culling test that returns individual culling reasons
static CullingResult test_meshlet_culling(MeshletDescriptor meshlet, float4x4 mvpMatrix, float3 cameraPosition) {
    CullingResult result;
    result.frustumCulled = is_meshlet_frustum_culled(meshlet, mvpMatrix);
    result.backfaceCulled = is_meshlet_backface_culled(meshlet, cameraPosition);
    result.isCulled = result.frustumCulled || result.backfaceCulled;
    return result;
}
